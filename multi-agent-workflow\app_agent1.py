import streamlit as st
import os
import re
from langchain_community.document_loaders import PyPDFLoader
from langchain_groq import ChatGroq
from dotenv import load_dotenv

load_dotenv()

# Initialize the LLM
llm = ChatGroq(model="llama3-70b-8192")

def extract_candidate_name(pdf_path: str) -> str:
    try:
        data = PyPDFLoader(pdf_path).load()
        text = " ".join([page.page_content for page in data])[:2000]

        prompt = f"""Extract the candidate's full name from the resume text below. 
Only return the name — no extra words.

{text}"""

        result = llm.invoke(prompt)
        candidate_name = result.content.strip().split('\n')[0]
        return candidate_name

    except Exception as e:
        return f"❌ Error extracting name: {e}"

def main():
    st.title("📄 Resume Name Extractor with Groq LLM")

    resume_file = st.file_uploader("Upload Resume (PDF)", type=["pdf"])

    if resume_file:
        with open("Resume.pdf", "wb") as f:
            f.write(resume_file.read())
        st.success("✅ Resume uploaded.")

        if st.button("🧠 Extract Candidate Name"):
            st.info("Processing resume with LLM...")
            name = extract_candidate_name("Resume.pdf")
            st.write("### 👤 Extracted Name")
            st.success(name)

if __name__ == "__main__":
    main()
