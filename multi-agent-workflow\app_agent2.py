import streamlit as st
import os
from langchain_groq import ChatGroq
from langchain_community.document_loaders import PyPDFLoader
from dotenv import load_dotenv

load_dotenv()

llm = ChatGroq(model="llama3-70b-8192")

# ✅ Already tested
def extract_candidate_name(pdf_path: str) -> str:
    try:
        data = PyPDFLoader(pdf_path).load()
        text = " ".join([page.page_content for page in data])[:2000]
        prompt = f"""Extract the candidate's full name from the resume text below. Only return the name — no extra words.\n\n{text}"""
        result = llm.invoke(prompt)
        return result.content.strip().split('\n')[0]
    except Exception as e:
        return f"❌ Error extracting name: {e}"

# ✅ New
def extract_job_requirements(jd_path: str) -> str:
    try:
        with open(jd_path, "r", encoding="utf-8") as f:
            jd_text = f.read()
        prompt = f"""Extract and summarize the key job requirements from this job description, including skills, experience, education, and other qualifications:\n\n{jd_text}"""
        result = llm.invoke(prompt)
        return result.content.strip()
    except Exception as e:
        return f"❌ Error extracting job requirements: {e}"

def main():
    st.title("📄 Resume Matching (Step-by-Step Agents)")
    resume_file = st.file_uploader("Upload Resume (PDF)", type=["pdf"])
    jd_file = st.file_uploader("Upload Job Description (TXT)", type=["txt"])

    if resume_file:
        with open("Resume.pdf", "wb") as f:
            f.write(resume_file.read())
        st.success("✅ Resume uploaded.")

    if jd_file:
        with open("JobDescription.txt", "wb") as f:
            f.write(jd_file.read())
        st.success("✅ Job Description uploaded.")

    if os.path.exists("Resume.pdf") and os.path.exists("JobDescription.txt"):
        if st.button("🚀 Run Step 1: Extract Candidate Name"):
            name = extract_candidate_name("Resume.pdf")
            st.subheader("👤 Candidate Name")
            st.success(name)

        if st.button("📋 Run Step 2: Extract Job Requirements"):
            jd_summary = extract_job_requirements("JobDescription.txt")
            st.subheader("📌 Job Requirements Summary")
            st.markdown(jd_summary)

if __name__ == "__main__":
    main()
