import streamlit as st
import os
import re
from langchain_groq import ChatGroq
from langchain_community.document_loaders import PyPDFLoader, WebBaseLoader
from dotenv import load_dotenv

load_dotenv()

# Initialize the LLM
llm = ChatGroq(model="llama3-70b-8192")

# Function to extract candidate's name from the resume
def extract_candidate_name(pdf_path: str) -> str:
    try:
        data = PyPDFLoader(pdf_path).load()
        text = " ".join([page.page_content for page in data])[:2000]
        prompt = f"""Extract the candidate's full name from the resume text below. 
Only return the name — no extra words.

{text}"""
        with st.spinner("Extracting name with LLM..."):
            result = llm.invoke(prompt)
        return result.content.strip().split('\n')[0]
    except Exception as e:
        return f"❌ Error extracting name: {e}"

# Function to extract key job requirements from the job description
def extract_job_requirements(jd_path: str) -> str:
    try:
        with open(jd_path, "r", encoding="utf-8") as f:
            jd_text = f.read()[:3000]
        prompt = f"""Extract and summarize the key job requirements from this job description, 
including skills, experience, education, and other qualifications:\n\n{jd_text}"""
        with st.spinner("Extracting job requirements with LLM..."):
            result = llm.invoke(prompt)
        return result.content.strip()
    except Exception as e:
        return f"❌ Error extracting job requirements: {e}"

# Function to extract LinkedIn URL from the resume
def extract_linkedin_url_from_resume(pdf_path: str) -> str:
    try:
        data = PyPDFLoader(pdf_path).load()
        text = " ".join([page.page_content for page in data])[:3000]
        linkedin_patterns = [
            r'https?://(?:www\.)?linkedin\.com/in/[^\s\)\]]+',
            r'www\.linkedin\.com/in/[^\s\)\]]+',
            r'linkedin\.com/in/[^\s\)\]]+'
        ]
        for pattern in linkedin_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                linkedin_url = matches[0]
                if not linkedin_url.startswith("http"):
                    linkedin_url = "https://" + linkedin_url
                return f"🔗 Found via regex: {linkedin_url}"

        prompt = f"""Extract the LinkedIn profile URL from the resume text below.
If none found, return 'No LinkedIn URL found'. Focus on formats like linkedin.com/in/username:

{text}"""
        with st.spinner("Looking for LinkedIn URL using LLM..."):
            result = llm.invoke(prompt)
        answer = result.content.strip()
        if "linkedin.com" in answer.lower():
            url_match = re.search(r'https?://[^\s]+linkedin\.com[^\s\)\]]+', answer)
            if url_match:
                return f"🔗 Found via LLM: {url_match.group()}"
            alt_match = re.search(r'linkedin\.com/in/[^\s\)\]]+', answer)
            if alt_match:
                return f"🔗 Found via LLM: https://{alt_match.group()}"
        return "❌ No LinkedIn URL found in resume."
    except Exception as e:
        return f"❌ Error in LinkedIn extraction: {e}"

# Function to verify LinkedIn name with the resume
def verify_linkedin_name(resume_name: str, linkedin_url: str) -> str:
    try:
        if "linkedin.com/in/" not in linkedin_url.lower():
            return "❌ Invalid LinkedIn URL provided."
        with st.spinner("Scraping LinkedIn profile..."):
            loader = WebBaseLoader(linkedin_url)
            docs = loader.load()
        if not docs or not docs[0].page_content.strip():
            return "⚠️ Could not scrape LinkedIn profile. LinkedIn likely blocked access or returned no content."
        page_content = docs[0].page_content[:2000]
        prompt = f"""Extract the person's full name from this LinkedIn profile content:\n\n{page_content}"""
        result = llm.invoke(prompt)
        linkedin_name = result.content.strip()
        compare_prompt = f"""Compare the following names and determine if they are likely the same person:

Resume Name: {resume_name}
LinkedIn Name: {linkedin_name}

Return one of the following:
- MATCH: They are the same person.
- NO MATCH: They are likely different.

Also explain your reasoning in 1-2 sentences."""
        result2 = llm.invoke(compare_prompt)
        return f"🔍 {result2.content.strip()}"
    except Exception as e:
        return f"⚠️ LinkedIn scraping failed or blocked: {e}"

# Function to assess the recruiter fit
def recruiter_fit_assessment(resume_name: str, job_description: str, linkedin_name: str) -> str:
    prompt = f"""
    Based on the following resume and job description, assess if the candidate is a good fit for the job. 
    Include details on skills, experience, and alignment with the job role:
    
    Resume Name: {resume_name}
    Job Description: {job_description}
    LinkedIn Name: {linkedin_name}
    """
    result = llm.invoke(prompt)
    return result.content.strip()

# Main function to run the Streamlit app
def main():
    st.set_page_config(page_title="Resume Matcher", layout="wide")
    st.title("📄 Resume Matching – Step-by-Step Agents")

    col1, col2 = st.columns(2)
    with col1:
        resume_file = st.file_uploader("Upload Resume (PDF)", type=["pdf"])
        if resume_file:
            with open("Resume.pdf", "wb") as f:
                f.write(resume_file.read())
            st.success("✅ Resume uploaded.")
    with col2:
        jd_file = st.file_uploader("Upload Job Description (TXT)", type=["txt"])
        if jd_file:
            with open("JobDescription.txt", "wb") as f:
                f.write(jd_file.read())
            st.success("✅ Job Description uploaded.")

    resume_ready = os.path.exists("Resume.pdf")
    jd_ready = os.path.exists("JobDescription.txt")

    if "resume_name" not in st.session_state:
        st.session_state.resume_name = ""
    if "linkedin_url" not in st.session_state:
        st.session_state.linkedin_url = ""

    if resume_ready and st.button("🚀 Step 1: Extract Candidate Name"):
        name = extract_candidate_name("Resume.pdf")
        st.session_state.resume_name = name
        st.subheader("👤 Candidate Name")
        st.success(name)

    if resume_ready and jd_ready and st.button("📋 Step 2: Extract Job Requirements"):
        jd_summary = extract_job_requirements("JobDescription.txt")
        st.subheader("📌 Job Requirements Summary")
        st.markdown(jd_summary)

    if resume_ready and st.button("🔍 Step 3: Extract LinkedIn URL from Resume"):
        linkedin_url = extract_linkedin_url_from_resume("Resume.pdf")
        st.session_state.linkedin_url = linkedin_url
        st.subheader("🔗 LinkedIn URL")
        st.write(linkedin_url)

    if resume_ready and st.session_state.resume_name and st.session_state.linkedin_url:
        if st.button("🧠 Step 4: Verify LinkedIn Name"):
            result = verify_linkedin_name(
                st.session_state.resume_name,
                st.session_state.linkedin_url.replace("🔗 Found via regex: ", "").replace("🔗 Found via LLM: ", "")
            )
            st.subheader("✅ LinkedIn Verification Result")
            st.markdown(result)

            # Step 5: Recruiter Fit Assessment (Visible after LinkedIn match)
            if "MATCH" in result:
                if st.button("📈 Step 5: Recruiter Fit Assessment"):
                    fit_result = recruiter_fit_assessment(
                        st.session_state.resume_name,
                        jd_summary,
                        st.session_state.resume_name
                    )
                    st.subheader("👥 Recruiter Fit Assessment")
                    st.markdown(fit_result)

    with st.expander("📋 Paste LinkedIn Profile Content (Optional if scraping fails)"):
        pasted_profile = st.text_area("Paste LinkedIn content here:", height=250)
        if st.button("🧠 Compare Names (Manual Mode)") and pasted_profile:
            with st.spinner("Comparing pasted LinkedIn content..."):
                prompt = f"""Compare these names and determine if they are the same person:

Resume Name: {st.session_state.resume_name}
LinkedIn Profile Content: {pasted_profile}

Extract the LinkedIn name and compare. Respond with 'MATCH' or 'NO MATCH' and explain."""
                result = llm.invoke(prompt)
                st.subheader("🔍 Manual Name Match Result")
                st.markdown(result.content.strip())

    # Reset button to clear session and restart
    if st.button("🔄 Reset"):
        st.session_state.clear()
        st.experimental_rerun()

if __name__ == "__main__":
    main()
