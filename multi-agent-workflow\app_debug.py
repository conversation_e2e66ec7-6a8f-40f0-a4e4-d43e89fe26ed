import streamlit as st
import os

def main():
    st.title("📄 Resume Matching App (Debug Version)")
    st.markdown("This is a basic version to confirm file uploads and UI rendering.")

    col1, col2 = st.columns(2)

    with col1:
        resume_file = st.file_uploader("Upload Resume (PDF)", type=["pdf"])
        if resume_file:
            with open("Resume.pdf", "wb") as f:
                f.write(resume_file.read())
            st.success("✅ Resume uploaded successfully.")
            st.write(f"Uploaded file: {resume_file.name}")

    with col2:
        jd_file = st.file_uploader("Upload Job Description (TXT)", type=["txt"])
        if jd_file:
            with open("JobDescription.txt", "wb") as f:
                f.write(jd_file.read())
            st.success("✅ Job Description uploaded successfully.")
            st.write(f"Uploaded file: {jd_file.name}")

    if os.path.exists("Resume.pdf") and os.path.exists("JobDescription.txt"):
        st.write("🚀 Ready to run workflow!")
    else:
        st.info("📌 Please upload both files to proceed.")

if __name__ == "__main__":
    main()
