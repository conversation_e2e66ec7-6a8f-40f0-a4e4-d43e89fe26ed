# Suppress warnings for cleaner output
import warnings
warnings.filterwarnings("ignore")

# Import LangGraph tools for multi-agent workflow orchestration
from langgraph.graph import END, START, StateGraph, add_messages

# Import the Groq Llama 3 chat model interface
from langchain_groq import ChatGroq

# Typing utilities for strong typing and agent state management
from typing import Annotated, TypedDict, Sequence

# HuggingFace instruct embeddings for semantic search
from langchain_community.embeddings import HuggingFaceInstructEmbeddings

# Base class for LLM messages
from langchain_core.messages import BaseMessage

# For splitting text into manageable chunks
from langchain.text_splitter import CharacterTextSplitter

# For conversational memory (context retention)
from langchain.memory import ConversationBufferMemory

# Loader for extracting text from PDF resumes
from langchain_community.document_loaders import PyPDFLoader

# Chroma vector database for semantic search
from langchain_community.vectorstores import Chroma

# Conversational retrieval chain for RAG (Retrieval Augmented Generation)
from langchain.chains import ConversationalRetrievalChain

# Loader for scraping web pages (e.g., LinkedIn)
from langchain_community.document_loaders import WebBaseLoader

# Streamlit for web app UI
import streamlit as st

# For image manipulation
from PIL import Image

# For loading environment variables (e.g., API keys)
from dotenv import load_dotenv
load_dotenv()  # Loads variables from a .env file into the environment

# Instantiate the Llama 3 LLM via Groq API
llm = ChatGroq(model="llama3-70b-8192")

# Utility function to load and return an image (for workflow visualization)
def load_image(image_file):
    img = Image.open(image_file)
    return img

# TypedDict to define the state passed between agents
class AgentState(TypedDict):
    # 'messages' is a sequence of BaseMessage objects; add_messages allows appending
    messages: Annotated[Sequence[BaseMessage], add_messages]
    # Additional state for storing intermediate results
    candidate_name: str
    job_requirements: str
    linkedin_url: str
    linkedin_verification: str
    final_judgment: str

# RAG (Retrieval Augmented Generation) agent function
def rag_output(state: AgentState):
    # Load the resume PDF file
    pdf_file = "Resume.pdf"
    data = PyPDFLoader(pdf_file).load()
    
    # Set up HuggingFace instruct embeddings for semantic search
    model_name = "hkunlp/instructor-large"
    model_kwargs = {'device': 'cpu'}
    encode_kwargs = {'normalize_embeddings': True}
    embeddings = HuggingFaceInstructEmbeddings(
        model_name=model_name, 
        model_kwargs=model_kwargs,
        encode_kwargs=encode_kwargs,
    )
    
    # Set up conversational memory for context retention
    memory = ConversationBufferMemory(memory_key='chat_history', return_messages=True)
    # Split the resume into manageable chunks for vector storage
    text_splitter = CharacterTextSplitter(chunk_size=300, chunk_overlap=100)

    try:
        # Split the loaded PDF data into chunks
        data_chunks = text_splitter.split_documents(data)
        
        # Create a Chroma vector store from the resume chunks
        vectorstore = Chroma.from_documents(
            documents=data_chunks,
            collection_name="my-rag-chroma",
            embedding=embeddings,
            persist_directory="db"
        )
        
        # Set up a conversational retrieval chain (RAG) using the LLM and vector store
        conversation_chain = ConversationalRetrievalChain.from_llm(
            llm=llm,
            chain_type="stuff",
            retriever=vectorstore.as_retriever(),
            memory=memory
        )
      
        # Query the chain for the candidate's LinkedIn profile link
        query = "What is the LinkedIn profile link of the candidate"
        result = conversation_chain.invoke({"question": query})
        answer = result["answer"]
        
    except Exception as ex:
        print("Errors = {}".format(ex))
        answer = "Error extracting LinkedIn profile."

    # Return the answer as a message in the agent state
    return {"messages": [answer]}

# Resume Agent function: extracts candidate name from resume
def agent(state: AgentState):
    try:
        pdf_file = "Resume.pdf"
        # Load the resume PDF
        data = PyPDFLoader(pdf_file).load()

        # Extract text from each page and combine into a single string
        string_text = [data[i].page_content for i in range(len(data))]
        resume_data = " ".join([text for text in string_text])

        # Use the LLM to extract the candidate's name
        prompt = "Extract the candidate's full name from the following resume:\n" + resume_data
        result = llm.invoke(prompt)
        candidate_name = result.content

    except Exception as ex:
        print("Error extracting name: {}".format(ex))
        candidate_name = "Error extracting name."

    return {"messages": [candidate_name]}

# Job Description Agent: extracts job requirements from the job description file
def job_description_agent(state: AgentState):
    try:
        # Load the job description from a text file
        with open("JobDescription.txt", "r", encoding="utf-8") as f:
            jd_text = f.read()

        # Use the LLM to extract the key requirements from the job description
        prompt = "Extract the exact job requirements from the following job description:\n" + jd_text
        result = llm.invoke(prompt)
        job_requirements = result.content

    except Exception as ex:
        print("Error extracting job requirements: {}".format(ex))
        job_requirements = "Error extracting job requirements."

    return {"messages": [job_requirements]}

# Web Scraping Agent: verifies LinkedIn profile and matches name
def web_scrap_agent(state: AgentState):
    try:
        # Get the LinkedIn profile URL from the previous agent's message
        linkedin_url = state["messages"][-1]

        # Use WebBaseLoader to scrape the LinkedIn profile page
        loader = WebBaseLoader(linkedin_url)
        docs = loader.load()

        # Extract the main page content as text
        page_content = docs[0].page_content

        # Use the LLM to extract the name from the LinkedIn profile
        prompt = "Extract the candidate's name from the following LinkedIn profile page content:\n" + page_content
        result = llm.invoke(prompt)
        linkedin_name = result.content

        # For demonstration, assume the resume name is available in state (could be passed explicitly)
        resume_name = state["messages"][0]

        # Compare the names for consistency
        if resume_name.lower() in linkedin_name.lower():
            match_result = f"LinkedIn profile exists and name matches: {linkedin_name}"
        else:
            match_result = f"LinkedIn profile exists but name does not match. Extracted name: {linkedin_name}"

    except Exception as ex:
        print("Error scraping LinkedIn: {}".format(ex))
        match_result = "Error scraping LinkedIn profile."

    return {"messages": [match_result]}

# Recruiter Agent: aggregates all info and makes the final fit judgment
def recruiter_agent(state: AgentState):
    try:
        # Collect all relevant info from previous agents
        resume_name = state["messages"][0]
        job_requirements = state["messages"][1]
        linkedin_result = state["messages"][2]

        # Compose a prompt for the LLM to make a final fit judgment
        prompt = (
            f"You are a recruitment expert. "
            f"Candidate Name: {resume_name}\n"
            f"Job Requirements: {job_requirements}\n"
            f"LinkedIn Result: {linkedin_result}\n"
            f"Based on the above, evaluate if the candidate is a good fit for the job. "
            f"Provide a detailed explanation and a final judgment."
        )
        result = llm.invoke(prompt)
        final_judgment = result.content

    except Exception as ex:
        print("Error in recruiter agent: {}".format(ex))
        final_judgment = "Error making final judgment."

    return {"messages": [final_judgment]}

# Create the LangGraph workflow
def create_workflow():
    """Create and return the LangGraph workflow"""
    workflow = StateGraph(AgentState)

    # Add nodes for each agent
    workflow.add_node("resume_agent", agent)
    workflow.add_node("job_description_agent", job_description_agent)
    workflow.add_node("rag_agent", rag_output)
    workflow.add_node("web_scraping_agent", web_scrap_agent)
    workflow.add_node("recruiter_agent", recruiter_agent)

    # Define the workflow edges
    workflow.add_edge(START, "resume_agent")
    workflow.add_edge("resume_agent", "job_description_agent")
    workflow.add_edge("job_description_agent", "rag_agent")
    workflow.add_edge("rag_agent", "web_scraping_agent")
    workflow.add_edge("web_scraping_agent", "recruiter_agent")
    workflow.add_edge("recruiter_agent", END)

    # Compile the workflow
    return workflow.compile()

# Main function: sets up and runs the Streamlit app
def main():
    # Set the Streamlit app title
    st.title("Resume Matching App with Multi-Agent Workflow")

    # Upload the resume PDF
    resume_file = st.file_uploader("Upload Resume (PDF)", type=["pdf"])
    if resume_file is not None:
        # Save the uploaded PDF locally
        with open("Resume.pdf", "wb") as f:
            f.write(resume_file.read())
        st.success("Resume uploaded successfully.")

    # Upload the job description text file
    jd_file = st.file_uploader("Upload Job Description (TXT)", type=["txt"])
    if jd_file is not None:
        # Save the uploaded JD locally
        with open("JobDescription.txt", "wb") as f:
            f.write(jd_file.read())
        st.success("Job Description uploaded successfully.")

    # Button to trigger the resume matching workflow
    if st.button("Match Resume"):
        # Display a placeholder for the workflow image (if available)
        # st.image(load_image("workflow.png"), caption="Agent Workflow", use_column_width=True)

        # Create the LangGraph workflow
        workflow = create_workflow()

        # Initialize the agent state with an empty message list
        initial_state = {
            "messages": [],
            "candidate_name": "",
            "job_requirements": "",
            "linkedin_url": "",
            "linkedin_verification": "",
            "final_judgment": ""
        }

        # Run the workflow
        st.write("Running Multi-Agent Workflow...")

        try:
            # Execute the workflow
            final_state = workflow.invoke(initial_state)

            # Display results
            st.write("**Workflow Results:**")
            for i, message in enumerate(final_state["messages"]):
                agent_names = ["Resume Agent", "Job Description Agent", "RAG Agent", "Web Scraping Agent", "Recruiter Agent"]
                if i < len(agent_names):
                    st.write(f"**{agent_names[i]} Output:** {message}")

        except Exception as e:
            st.error(f"Error running workflow: {str(e)}")

            # Fallback to sequential execution
            st.write("Falling back to sequential execution...")
            agent_state = {"messages": []}

            # Run each agent in sequence and collect their outputs
            # 1. Resume Agent
            resume_output = agent(agent_state)
            st.write("Resume Agent Output:", resume_output["messages"][0])
            agent_state["messages"].append(resume_output["messages"][0])

            # 2. Job Description Agent
            jd_output = job_description_agent(agent_state)
            st.write("Job Description Agent Output:", jd_output["messages"][0])
            agent_state["messages"].append(jd_output["messages"][0])

            # 3. RAG Agent (extracts LinkedIn info)
            rag_output_result = rag_output(agent_state)
            st.write("RAG Agent Output:", rag_output_result["messages"][0])
            agent_state["messages"].append(rag_output_result["messages"][0])

            # 4. Web Scraping Agent (checks LinkedIn profile and name)
            web_scrap_output = web_scrap_agent(agent_state)
            st.write("Web Scraping Agent Output:", web_scrap_output["messages"][0])
            agent_state["messages"].append(web_scrap_output["messages"][0])

            # 5. Recruiter Agent (final fit judgment)
            recruiter_output = recruiter_agent(agent_state)
            st.write("Recruiter Agent Output:", recruiter_output["messages"][0])

# Entry point for the script
if __name__ == "__main__":
    main()
