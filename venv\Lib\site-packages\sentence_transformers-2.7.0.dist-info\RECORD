sentence_transformers-2.7.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
sentence_transformers-2.7.0.dist-info/LICENSE,sha256=Oh9H74dgL8mR6roWG-jR2egY_IPFDYVpurb1ncnesyw,11539
sentence_transformers-2.7.0.dist-info/METADATA,sha256=bXOvC7uXQ0MpLvdF3bNOMcMs6X7CRYHdF3GJMaKOTts,11781
sentence_transformers-2.7.0.dist-info/NOTICE.txt,sha256=b2uTp6MMZfiS6jgdaPfV8ucGvzc2jpzaqOyvOvId9rA,254
sentence_transformers-2.7.0.dist-info/RECORD,,
sentence_transformers-2.7.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sentence_transformers-2.7.0.dist-info/WHEEL,sha256=GJ7t_kWBFywbagK5eo9IoUwLW6oyOeTKmQ-9iHFVNxQ,92
sentence_transformers-2.7.0.dist-info/top_level.txt,sha256=G9jWBWwTz-uxA1H2fuPmBn8PuLhP2SsPF-RsCYpjJ6E,22
sentence_transformers/LoggingHandler.py,sha256=HEQ9QgoFY1gNePOq5gvcH76yb_RpC9jxZDmgqATNbQk,1824
sentence_transformers/SentenceTransformer.py,sha256=MiP-Tg0HWIobPrprjW3lxCNt712x-hj8GgwlosXsins,65209
sentence_transformers/__init__.py,sha256=l7QX0In91_EqSpPgrUV04W-8zXwgL3TMHt7UcfVDKKM,574
sentence_transformers/__pycache__/LoggingHandler.cpython-311.pyc,,
sentence_transformers/__pycache__/SentenceTransformer.cpython-311.pyc,,
sentence_transformers/__pycache__/__init__.cpython-311.pyc,,
sentence_transformers/__pycache__/model_card_templates.cpython-311.pyc,,
sentence_transformers/__pycache__/quantization.cpython-311.pyc,,
sentence_transformers/__pycache__/util.cpython-311.pyc,,
sentence_transformers/cross_encoder/CrossEncoder.py,sha256=zK8uuq6v_852OMUw3JnxhprzE40ok00x3XwT-RvN-A8,23894
sentence_transformers/cross_encoder/__init__.py,sha256=X5LoeCzcUIvY7u-uwb-Xc6pUgNSUD5Zn18d7Tyr3diE,70
sentence_transformers/cross_encoder/__pycache__/CrossEncoder.cpython-311.pyc,,
sentence_transformers/cross_encoder/__pycache__/__init__.cpython-311.pyc,,
sentence_transformers/cross_encoder/evaluation/CEBinaryAccuracyEvaluator.py,sha256=9KwvLhUegHL4IaAojPqzNeQxYsGA2neLxhXCVy3cHqM,2751
sentence_transformers/cross_encoder/evaluation/CEBinaryClassificationEvaluator.py,sha256=Hr2dJOddFSfB5DbAcC68FR3aOxQV8s5oel08YwQ2KWI,3911
sentence_transformers/cross_encoder/evaluation/CECorrelationEvaluator.py,sha256=gtN_9d7yQjknFGTCUhA_RYv4CElVtujEo-xega0_jjQ,2586
sentence_transformers/cross_encoder/evaluation/CEF1Evaluator.py,sha256=o9haO-zMF65kZ4stlLa6nXijisR-c6eXkR3itjgXO2g,4576
sentence_transformers/cross_encoder/evaluation/CERerankingEvaluator.py,sha256=DTc2whIdx6_M5H-qy2dsVZRNrxzzXYQ5KonO80Qcz3Q,4564
sentence_transformers/cross_encoder/evaluation/CESoftmaxAccuracyEvaluator.py,sha256=_0Vo9eDe_FUoFXuwVFJ6TBnGfhA21WSNpVBvLV2cXqQ,2470
sentence_transformers/cross_encoder/evaluation/__init__.py,sha256=yTJD2uAFBAEh9wKAfZYIW7hiz1j-yRUcj_OAjje8zII,579
sentence_transformers/cross_encoder/evaluation/__pycache__/CEBinaryAccuracyEvaluator.cpython-311.pyc,,
sentence_transformers/cross_encoder/evaluation/__pycache__/CEBinaryClassificationEvaluator.cpython-311.pyc,,
sentence_transformers/cross_encoder/evaluation/__pycache__/CECorrelationEvaluator.cpython-311.pyc,,
sentence_transformers/cross_encoder/evaluation/__pycache__/CEF1Evaluator.cpython-311.pyc,,
sentence_transformers/cross_encoder/evaluation/__pycache__/CERerankingEvaluator.cpython-311.pyc,,
sentence_transformers/cross_encoder/evaluation/__pycache__/CESoftmaxAccuracyEvaluator.cpython-311.pyc,,
sentence_transformers/cross_encoder/evaluation/__pycache__/__init__.cpython-311.pyc,,
sentence_transformers/datasets/DenoisingAutoEncoderDataset.py,sha256=URfedYVxl_pMiuhPq9l3wgNItkfHei9_mqO1sSs1_Zw,1746
sentence_transformers/datasets/NoDuplicatesDataLoader.py,sha256=QuJGxTXsEdtB_7fizc15NSoO5Q4DyEaF_r1PeLqstBk,1561
sentence_transformers/datasets/ParallelSentencesDataset.py,sha256=u-m2sEWJH5Q1V-WXi37sUrFHLBCMIm6Ulq6OUyswY9s,7331
sentence_transformers/datasets/SentenceLabelDataset.py,sha256=-CjMt_YScCDOR_FRcBvsMOSswPQljuRsupyBomAmSAg,4169
sentence_transformers/datasets/SentencesDataset.py,sha256=qmiWGVpprXz8C6bIdhmNV2zW_0KfLeh2elB9fzh8lAc,687
sentence_transformers/datasets/__init__.py,sha256=ocP8DuAV1wT81RqGtO1piQgFTMFsJTeF-3Nbx_oJPpg,470
sentence_transformers/datasets/__pycache__/DenoisingAutoEncoderDataset.cpython-311.pyc,,
sentence_transformers/datasets/__pycache__/NoDuplicatesDataLoader.cpython-311.pyc,,
sentence_transformers/datasets/__pycache__/ParallelSentencesDataset.cpython-311.pyc,,
sentence_transformers/datasets/__pycache__/SentenceLabelDataset.cpython-311.pyc,,
sentence_transformers/datasets/__pycache__/SentencesDataset.cpython-311.pyc,,
sentence_transformers/datasets/__pycache__/__init__.cpython-311.pyc,,
sentence_transformers/evaluation/BinaryClassificationEvaluator.py,sha256=fjvlit2yni_SSnWZNpKEUhnblJL8u5bgguKcPoyIvxA,11329
sentence_transformers/evaluation/EmbeddingSimilarityEvaluator.py,sha256=4ehzyh2ZzM8JRCEXrIbG7swU1c1odnDzn97iYWOdylQ,8972
sentence_transformers/evaluation/InformationRetrievalEvaluator.py,sha256=73KrOHfAluuL57q70c6BkOvJ_aooXiVJeOtOfOcrL4Y,14685
sentence_transformers/evaluation/LabelAccuracyEvaluator.py,sha256=M-rgAzzL2TFWTzzRfkuk-JZzHVVq6dz5WZJfCgCQaZQ,3003
sentence_transformers/evaluation/MSEEvaluator.py,sha256=VcBIMipSHChBw5J15RhTVGZ4knNeJGiFRAAoky_WRb8,3984
sentence_transformers/evaluation/MSEEvaluatorFromDataFrame.py,sha256=o4-0ssw3Vq0vGHyFDVvltFLbDH6aCOxxQk_OZXVOmNA,4811
sentence_transformers/evaluation/ParaphraseMiningEvaluator.py,sha256=WGhNeP-0lz0YpcE0mzEixOE40qtPwaa9QWP7TEUwB9U,9540
sentence_transformers/evaluation/RerankingEvaluator.py,sha256=IHvu9T4qNeuNlXblJS5TODBeRbfnSqlALSS3X2j8KsA,10735
sentence_transformers/evaluation/SentenceEvaluator.py,sha256=R8eRbODPCwAWVjY9pwsaBaewUglaAwVrzfzu4F7elgE,1191
sentence_transformers/evaluation/SequentialEvaluator.py,sha256=NhTYuIsZlyaPu9o7kANEs-wWfQ4aXhSQ0GDb3xtrDwo,953
sentence_transformers/evaluation/SimilarityFunction.py,sha256=caOhzEnWUrCntxNWn_oVRBQ6cFd4GUhnXr8wfh_WOd4,135
sentence_transformers/evaluation/TranslationEvaluator.py,sha256=VtDNLbxXXVugrrdMnlro8PlGnBBGjZjhADf3oMK4bzI,5766
sentence_transformers/evaluation/TripletEvaluator.py,sha256=5yovq2QR4enFjwWKxlOfLRTV-MkZzIlQQkkFO5DAwqU,7351
sentence_transformers/evaluation/__init__.py,sha256=-LiNxso5PUr0k_a9C5rxCLwgu975igr-f4qZ6gF5NPc,1177
sentence_transformers/evaluation/__pycache__/BinaryClassificationEvaluator.cpython-311.pyc,,
sentence_transformers/evaluation/__pycache__/EmbeddingSimilarityEvaluator.cpython-311.pyc,,
sentence_transformers/evaluation/__pycache__/InformationRetrievalEvaluator.cpython-311.pyc,,
sentence_transformers/evaluation/__pycache__/LabelAccuracyEvaluator.cpython-311.pyc,,
sentence_transformers/evaluation/__pycache__/MSEEvaluator.cpython-311.pyc,,
sentence_transformers/evaluation/__pycache__/MSEEvaluatorFromDataFrame.cpython-311.pyc,,
sentence_transformers/evaluation/__pycache__/ParaphraseMiningEvaluator.cpython-311.pyc,,
sentence_transformers/evaluation/__pycache__/RerankingEvaluator.cpython-311.pyc,,
sentence_transformers/evaluation/__pycache__/SentenceEvaluator.cpython-311.pyc,,
sentence_transformers/evaluation/__pycache__/SequentialEvaluator.cpython-311.pyc,,
sentence_transformers/evaluation/__pycache__/SimilarityFunction.cpython-311.pyc,,
sentence_transformers/evaluation/__pycache__/TranslationEvaluator.cpython-311.pyc,,
sentence_transformers/evaluation/__pycache__/TripletEvaluator.cpython-311.pyc,,
sentence_transformers/evaluation/__pycache__/__init__.cpython-311.pyc,,
sentence_transformers/losses/AdaptiveLayerLoss.py,sha256=BPsZ-sHMrpkafMUua5oOEV77zW8CbmBwYLbLW51eQgY,11065
sentence_transformers/losses/AnglELoss.py,sha256=JmbAT02Fv1joaH_lggfDTT8YIrgZueo6yYkN1gig2Ag,3059
sentence_transformers/losses/BatchAllTripletLoss.py,sha256=9q2CGieg7UPnMKwx8097w59Chr2LY4WyQIPS-7iOA88,6110
sentence_transformers/losses/BatchHardSoftMarginTripletLoss.py,sha256=XZ4PAxC_r-7cyL5j5DZDACLWn2886g4HTDSER2i8eEM,6664
sentence_transformers/losses/BatchHardTripletLoss.py,sha256=qVwWy9pmWqCxIy5rH4d3l-q8FfbyNMiARGW__aaZ6ek,11614
sentence_transformers/losses/BatchSemiHardTripletLoss.py,sha256=qU3JXgK2D9PzSOMfENyfMt9JPe0A3Gl71875hw8IBcM,7836
sentence_transformers/losses/CachedGISTEmbedLoss.py,sha256=2vyjHArH6enNecK0Zru9xFQ7FCc2V3XWZpRVQQqR_og,15064
sentence_transformers/losses/CachedMultipleNegativesRankingLoss.py,sha256=E9G-TOAyeXHQWFkAZrqrKCpCdfeogN5Dgf71bb7xTmg,12020
sentence_transformers/losses/CoSENTLoss.py,sha256=tzVyTBMuf6SnMQd0hS4UxP5mqajlBpXZ5YAZgw6RbN0,4250
sentence_transformers/losses/ContrastiveLoss.py,sha256=KoUh4B3i6DBEPR1n0xv2VUDUspQUP5pH3Fjz8YvAI9w,4463
sentence_transformers/losses/ContrastiveTensionLoss.py,sha256=W09ksQr-COLWjCIXBGSXYE8j6aqf8thjpwycRjMTbdQ,10205
sentence_transformers/losses/CosineSimilarityLoss.py,sha256=gM49wM7LpX8mXHYWVXJi-5CxIb1icZyF4FEBvJi1CRg,3558
sentence_transformers/losses/DenoisingAutoEncoderLoss.py,sha256=lBoYs6nxVwYn5NjdMzp-N4BbG1DJ9fSyYpm-TmDyrMw,8827
sentence_transformers/losses/GISTEmbedLoss.py,sha256=p0PnhxdL9Z5YVYJibrxZxJM_QhQfe94z6AIRfq65EOQ,7099
sentence_transformers/losses/MSELoss.py,sha256=4k43OBQgYYxnNxU5AV2Se92y2OIKfu3X60rxRQed_Xk,3166
sentence_transformers/losses/MarginMSELoss.py,sha256=QQZsUqX0L4TgW5eIAZHWgoYBs5572wYKkyIV5nlCuuY,4685
sentence_transformers/losses/Matryoshka2dLoss.py,sha256=viTojeRRZYKJTaHT4itJdDAxochWKo-1X6l2sdBtBHs,6014
sentence_transformers/losses/MatryoshkaLoss.py,sha256=7hVlfM6S9U-Rg-SiRBgcALnE_iSWdPh-Vun8-csEIWQ,6310
sentence_transformers/losses/MegaBatchMarginLoss.py,sha256=8xuboNFb9h8TcwxmRJaX83xE4yZSQTsWU8Wn8Od3jwU,6931
sentence_transformers/losses/MultipleNegativesRankingLoss.py,sha256=unBD008EKKfZFJPYtoe3RtjvWKU4Y1_RfTBViEE0xqs,5463
sentence_transformers/losses/MultipleNegativesSymmetricRankingLoss.py,sha256=6eifob88pSSYW2c4K7LXYaWKjy2YRLqiBd_3luXSaQ0,3853
sentence_transformers/losses/OnlineContrastiveLoss.py,sha256=LShKy2j_5thaIc4aSYbtF5C7Ndf6STP_gZhADJ6yvTc,3899
sentence_transformers/losses/SoftmaxLoss.py,sha256=JDU0U91gbnnuUTWcRGhcMd9j2t7NDHqFFeXkUBt08zY,5417
sentence_transformers/losses/TripletLoss.py,sha256=3izLXcgFQpAUofbmLvYXZ8CSqFAAHuAqW6wuEGlmbNM,4027
sentence_transformers/losses/__init__.py,sha256=BAMYUdBO-1E8BMkQyaxc1mrg5ix20fptyju_UYWt5RY,2361
sentence_transformers/losses/__pycache__/AdaptiveLayerLoss.cpython-311.pyc,,
sentence_transformers/losses/__pycache__/AnglELoss.cpython-311.pyc,,
sentence_transformers/losses/__pycache__/BatchAllTripletLoss.cpython-311.pyc,,
sentence_transformers/losses/__pycache__/BatchHardSoftMarginTripletLoss.cpython-311.pyc,,
sentence_transformers/losses/__pycache__/BatchHardTripletLoss.cpython-311.pyc,,
sentence_transformers/losses/__pycache__/BatchSemiHardTripletLoss.cpython-311.pyc,,
sentence_transformers/losses/__pycache__/CachedGISTEmbedLoss.cpython-311.pyc,,
sentence_transformers/losses/__pycache__/CachedMultipleNegativesRankingLoss.cpython-311.pyc,,
sentence_transformers/losses/__pycache__/CoSENTLoss.cpython-311.pyc,,
sentence_transformers/losses/__pycache__/ContrastiveLoss.cpython-311.pyc,,
sentence_transformers/losses/__pycache__/ContrastiveTensionLoss.cpython-311.pyc,,
sentence_transformers/losses/__pycache__/CosineSimilarityLoss.cpython-311.pyc,,
sentence_transformers/losses/__pycache__/DenoisingAutoEncoderLoss.cpython-311.pyc,,
sentence_transformers/losses/__pycache__/GISTEmbedLoss.cpython-311.pyc,,
sentence_transformers/losses/__pycache__/MSELoss.cpython-311.pyc,,
sentence_transformers/losses/__pycache__/MarginMSELoss.cpython-311.pyc,,
sentence_transformers/losses/__pycache__/Matryoshka2dLoss.cpython-311.pyc,,
sentence_transformers/losses/__pycache__/MatryoshkaLoss.cpython-311.pyc,,
sentence_transformers/losses/__pycache__/MegaBatchMarginLoss.cpython-311.pyc,,
sentence_transformers/losses/__pycache__/MultipleNegativesRankingLoss.cpython-311.pyc,,
sentence_transformers/losses/__pycache__/MultipleNegativesSymmetricRankingLoss.cpython-311.pyc,,
sentence_transformers/losses/__pycache__/OnlineContrastiveLoss.cpython-311.pyc,,
sentence_transformers/losses/__pycache__/SoftmaxLoss.cpython-311.pyc,,
sentence_transformers/losses/__pycache__/TripletLoss.cpython-311.pyc,,
sentence_transformers/losses/__pycache__/__init__.cpython-311.pyc,,
sentence_transformers/model_card_templates.py,sha256=xhp4DXy8pAmKkltKoQeRkkty3FRAmktps55nAdXj2dE,5839
sentence_transformers/models/Asym.py,sha256=teQ3za50KETdXc5ebI2SjvDCFls3pmVdFOCDy0ISG6A,5692
sentence_transformers/models/BoW.py,sha256=tgArRxekYWcWn2_eOx_VJKYcmrQhao2Z42oYv7-tthk,3396
sentence_transformers/models/CLIPModel.py,sha256=uxgwP8VU4j0nIvSRMLZgyns_K9PRbfKBxIyRHl5nZIc,3002
sentence_transformers/models/CNN.py,sha256=E3d5dHViNloeEIUCLSgrJP41ywDcMrIYyWkn_wcyUM8,2632
sentence_transformers/models/Dense.py,sha256=S1ItGGxZ_qQH1XFcRHhiUz_pVUGf-qSEqVrs98BIhU4,2812
sentence_transformers/models/Dropout.py,sha256=MfvU2PNfskQY4CRdG0cuOVj-HJhbz0w0epo5_J31qyQ,958
sentence_transformers/models/LSTM.py,sha256=4sFJYFpuHcWVWlFR5clWtp84KTdAbn3Av0pORqlD6YE,2575
sentence_transformers/models/LayerNorm.py,sha256=FTzfj4NVjJsKZx8mEUISH3SdmSaQNNjsR7QA9H13lqs,1187
sentence_transformers/models/Normalize.py,sha256=ZDjLbSgWVPE9j3PNH9ivvkqaoQC4tu-QBJInc-BJPT8,592
sentence_transformers/models/Pooling.py,sha256=OAHNYIBUDsIJk0FC0-0h0pg_eJaJh8IXbP30lwCGJSI,10679
sentence_transformers/models/Transformer.py,sha256=OxXBqXp2psU-AEj8SqzDk2NipZ7BlPiKjgeGBD9Hci8,7939
sentence_transformers/models/WeightedLayerPooling.py,sha256=bKp2Qm1TrKBzaBuR0D4papG2zjx3YKL9mjk8FRO0pYU,2364
sentence_transformers/models/WordEmbeddings.py,sha256=idrL2EiZpNRi5GU1EUtKrAx98tqbnqGLuS-xk9w_Smg,6369
sentence_transformers/models/WordWeights.py,sha256=dR2hbyoJwh2gIkmW_w3NfEnTeP_sxs5OXymALXsjjAA,3159
sentence_transformers/models/__init__.py,sha256=AmJsc86tXt5vCaNtHZGYRS-2kMWQ-cvfDVt-B2Bq-Ig,716
sentence_transformers/models/__pycache__/Asym.cpython-311.pyc,,
sentence_transformers/models/__pycache__/BoW.cpython-311.pyc,,
sentence_transformers/models/__pycache__/CLIPModel.cpython-311.pyc,,
sentence_transformers/models/__pycache__/CNN.cpython-311.pyc,,
sentence_transformers/models/__pycache__/Dense.cpython-311.pyc,,
sentence_transformers/models/__pycache__/Dropout.cpython-311.pyc,,
sentence_transformers/models/__pycache__/LSTM.cpython-311.pyc,,
sentence_transformers/models/__pycache__/LayerNorm.cpython-311.pyc,,
sentence_transformers/models/__pycache__/Normalize.cpython-311.pyc,,
sentence_transformers/models/__pycache__/Pooling.cpython-311.pyc,,
sentence_transformers/models/__pycache__/Transformer.cpython-311.pyc,,
sentence_transformers/models/__pycache__/WeightedLayerPooling.cpython-311.pyc,,
sentence_transformers/models/__pycache__/WordEmbeddings.cpython-311.pyc,,
sentence_transformers/models/__pycache__/WordWeights.cpython-311.pyc,,
sentence_transformers/models/__pycache__/__init__.cpython-311.pyc,,
sentence_transformers/models/tokenizer/PhraseTokenizer.py,sha256=wh_SamQwT03Y-XqIGYbnxX4BvCwGy2oNidYFprMicKQ,4686
sentence_transformers/models/tokenizer/WhitespaceTokenizer.py,sha256=YsE8mkFJyvGc-iIQd4PpJZ9zwiq2NCfuVLEP4MQ1P5g,2445
sentence_transformers/models/tokenizer/WordTokenizer.py,sha256=qpax3QybcekxTcYvHqn_euW0QnCCeNBsZMw7Z2fxwaQ,5789
sentence_transformers/models/tokenizer/__init__.py,sha256=HVwdDtGcWo8PhthoBfU9OQH1syYhjZwCEHIVCoGOzic,257
sentence_transformers/models/tokenizer/__pycache__/PhraseTokenizer.cpython-311.pyc,,
sentence_transformers/models/tokenizer/__pycache__/WhitespaceTokenizer.cpython-311.pyc,,
sentence_transformers/models/tokenizer/__pycache__/WordTokenizer.cpython-311.pyc,,
sentence_transformers/models/tokenizer/__pycache__/__init__.cpython-311.pyc,,
sentence_transformers/quantization.py,sha256=LhBPN7S2KiUjNtHKGmTiA-rJq7hN1mYcj-8tLnUEUW8,19710
sentence_transformers/readers/InputExample.py,sha256=KrFCR_LqxJs372ZV4ZGca8CjYHN-7lync1EyCpq6dRM,734
sentence_transformers/readers/LabelSentenceReader.py,sha256=738ZEoq88VXtWxMMcEUGlX0LqdQdMjfiRHGVOmSf8DY,1286
sentence_transformers/readers/NLIDataReader.py,sha256=EKRgMu6knysTPW8meqOyOJkHEM9z-ww0KGSR94VM50A,1676
sentence_transformers/readers/PairedFilesReader.py,sha256=5bejh0xWFD8bMe7J65yO9fP8l0r9m9O8Clt6hGRd9_M,1127
sentence_transformers/readers/STSDataReader.py,sha256=cCr2hcJq73FYDfzrnTPlALNEro9h_DkHF_12a8Lx-qg,3015
sentence_transformers/readers/TripletReader.py,sha256=p06-6DKHjwiDb2TCNSt5ENRaf28FXca1mnIUwHprTtw,1443
sentence_transformers/readers/__init__.py,sha256=M5BSOXJeVzgVifwnsUGSnSipho6QcwZ8WV3nXX1BlPM,408
sentence_transformers/readers/__pycache__/InputExample.cpython-311.pyc,,
sentence_transformers/readers/__pycache__/LabelSentenceReader.cpython-311.pyc,,
sentence_transformers/readers/__pycache__/NLIDataReader.cpython-311.pyc,,
sentence_transformers/readers/__pycache__/PairedFilesReader.cpython-311.pyc,,
sentence_transformers/readers/__pycache__/STSDataReader.cpython-311.pyc,,
sentence_transformers/readers/__pycache__/TripletReader.cpython-311.pyc,,
sentence_transformers/readers/__pycache__/__init__.cpython-311.pyc,,
sentence_transformers/util.py,sha256=9m2qnPBBwOrpPalA9HlFVvZlvKO7cpMHVuMOT0GOh9E,24378
