# Suppress warnings for cleaner output
import warnings
warnings.filterwarnings("ignore")

# Import LangGraph tools for multi-agent workflow orchestration
from langgraph.graph import END, START, StateGraph, add_messages

# Import the Groq Llama 3 chat model interface
from langchain_groq import ChatGroq

# Typing utilities for strong typing and agent state management
from typing import Annotated, TypedDict, Sequence

# Alternative embedding options - choose one that works
try:
    from langchain_huggingface import HuggingFaceEmbeddings
    EMBEDDING_TYPE = "huggingface"
except ImportError:
    try:
        from langchain_community.embeddings import HuggingFaceEmbeddings
        EMBEDDING_TYPE = "huggingface"
    except ImportError:
        try:
            from langchain_openai import OpenAIEmbeddings
            EMBEDDING_TYPE = "openai"
        except ImportError:
            EMBEDDING_TYPE = None

# Base class for LLM messages
from langchain_core.messages import BaseMessage

# For splitting text into manageable chunks
from langchain.text_splitter import CharacterTextSplitter

# For conversational memory (context retention)
from langchain.memory import ConversationBufferMemory

# Loader for extracting text from PDF resumes
from langchain_community.document_loaders import PyPDFLoader

# Chroma vector database for semantic search
from langchain_community.vectorstores import Chroma

# Conversational retrieval chain for RAG (Retrieval Augmented Generation)
from langchain.chains import ConversationalRetrievalChain

# Loader for scraping web pages (e.g., LinkedIn)
from langchain_community.document_loaders import WebBaseLoader

# Streamlit for web app UI
import streamlit as st

# For image manipulation
from PIL import Image

# For loading environment variables (e.g., API keys)
from dotenv import load_dotenv
load_dotenv()  # Loads variables from a .env file into the environment

# Instantiate the Llama 3 LLM via Groq API
llm = ChatGroq(model="llama3-70b-8192")

# Utility function to load and return an image (for workflow visualization)
def load_image(image_file):
    img = Image.open(image_file)
    return img

# Function to get embeddings based on available libraries
def get_embeddings():
    """Get embeddings model based on what's available"""
    if EMBEDDING_TYPE == "huggingface":
        # Use regular HuggingFaceEmbeddings with compatible settings
        try:
            return HuggingFaceEmbeddings(
                model_name="sentence-transformers/all-MiniLM-L6-v2",
                model_kwargs={'device': 'cpu'},
                encode_kwargs={'normalize_embeddings': True}
            )
        except Exception as e:
            # If that fails, try with minimal parameters
            try:
                return HuggingFaceEmbeddings(
                    model_name="all-MiniLM-L6-v2"
                )
            except Exception as e2:
                # Last resort - use a different model
                return HuggingFaceEmbeddings(
                    model_name="paraphrase-MiniLM-L6-v2"
                )
    elif EMBEDDING_TYPE == "openai":
        # If you have OpenAI API key, use this
        return OpenAIEmbeddings()
    else:
        # Fallback - this will cause an error, but we'll handle it
        raise ImportError("No suitable embedding model found")

# TypedDict to define the state passed between agents
class AgentState(TypedDict):
    # 'messages' is a sequence of BaseMessage objects; add_messages allows appending
    messages: Annotated[Sequence[BaseMessage], add_messages]
    # Additional state for storing intermediate results
    candidate_name: str
    job_requirements: str
    linkedin_url: str
    linkedin_verification: str
    final_judgment: str

# RAG (Retrieval Augmented Generation) agent function
def rag_output(state: AgentState):
    # Load the resume PDF file
    pdf_file = "Resume.pdf"
    
    try:
        data = PyPDFLoader(pdf_file).load()
        
        # Get embeddings
        embeddings = get_embeddings()
        
        # Set up conversational memory for context retention
        memory = ConversationBufferMemory(memory_key='chat_history', return_messages=True)
        # Split the resume into manageable chunks for vector storage
        text_splitter = CharacterTextSplitter(chunk_size=300, chunk_overlap=100)

        # Split the loaded PDF data into chunks
        data_chunks = text_splitter.split_documents(data)
        
        # Create a Chroma vector store from the resume chunks
        # Use a unique collection name to avoid conflicts
        import uuid
        collection_name = f"rag-collection-{uuid.uuid4().hex[:8]}"
        
        vectorstore = Chroma.from_documents(
            documents=data_chunks,
            collection_name=collection_name,
            embedding=embeddings,
            persist_directory=None  # Don't persist to avoid conflicts
        )
        
        # Set up a conversational retrieval chain (RAG) using the LLM and vector store
        conversation_chain = ConversationalRetrievalChain.from_llm(
            llm=llm,
            chain_type="stuff",
            retriever=vectorstore.as_retriever(),
            memory=memory
        )
      
        # Query the chain for the candidate's LinkedIn profile link
        query = "What is the LinkedIn profile link of the candidate"
        result = conversation_chain.invoke({"question": query})
        answer = result["answer"]
        
    except Exception as ex:
        print("RAG Error = {}".format(ex))
        # Fallback: try to extract LinkedIn from raw text
        try:
            data = PyPDFLoader(pdf_file).load()
            string_text = [data[i].page_content for i in range(len(data))]
            resume_data = " ".join([text for text in string_text])
            
            # Use LLM directly to find LinkedIn URL
            prompt = "Extract the LinkedIn profile URL from the following resume text. If no LinkedIn URL is found, respond with 'No LinkedIn URL found':\n" + resume_data
            result = llm.invoke(prompt)
            answer = result.content
        except Exception as fallback_ex:
            print("Fallback error: {}".format(fallback_ex))
            answer = "Error extracting LinkedIn profile."

    # Return the answer as a message in the agent state
    return {"messages": [answer]}

# Resume Agent function: extracts candidate name from resume
def agent(state: AgentState):
    try:
        pdf_file = "Resume.pdf"
        # Load the resume PDF
        data = PyPDFLoader(pdf_file).load()

        # Extract text from each page and combine into a single string
        string_text = [data[i].page_content for i in range(len(data))]
        resume_data = " ".join([text for text in string_text])

        # Use the LLM to extract the candidate's name
        prompt = "Extract the candidate's full name from the following resume. Return only the name:\n" + resume_data
        result = llm.invoke(prompt)
        candidate_name = result.content.strip()

    except Exception as ex:
        print("Error extracting name: {}".format(ex))
        candidate_name = "Error extracting name."

    return {"messages": [candidate_name]}

# Job Description Agent: extracts job requirements from the job description file
def job_description_agent(state: AgentState):
    try:
        # Load the job description from a text file
        with open("JobDescription.txt", "r", encoding="utf-8") as f:
            jd_text = f.read()

        # Use the LLM to extract the key requirements from the job description
        prompt = "Extract the key job requirements and qualifications from the following job description:\n" + jd_text
        result = llm.invoke(prompt)
        job_requirements = result.content

    except Exception as ex:
        print("Error extracting job requirements: {}".format(ex))
        job_requirements = "Error extracting job requirements."

    return {"messages": [job_requirements]}

# Web Scraping Agent: verifies LinkedIn profile and matches name
def web_scrap_agent(state: AgentState):
    try:
        # Get the LinkedIn profile URL from the previous agent's message
        linkedin_url = state["messages"][-1].strip()
        
        # Check if we actually got a LinkedIn URL
        if "linkedin.com" not in linkedin_url.lower() or "no linkedin" in linkedin_url.lower():
            return {"messages": ["No valid LinkedIn URL found in resume."]}

        # Use WebBaseLoader to scrape the LinkedIn profile page
        loader = WebBaseLoader(linkedin_url)
        docs = loader.load()

        # Extract the main page content as text
        page_content = docs[0].page_content

        # Use the LLM to extract the name from the LinkedIn profile
        prompt = "Extract the person's name from the following LinkedIn profile page content. Return only the name:\n" + page_content[:2000]  # Limit content length
        result = llm.invoke(prompt)
        linkedin_name = result.content.strip()

        # Get the resume name from the first message
        resume_name = state["messages"][0].strip()

        # Compare the names for consistency
        comparison_prompt = f"Compare these two names and determine if they refer to the same person:\nName 1: {resume_name}\nName 2: {linkedin_name}\nRespond with 'MATCH' or 'NO MATCH' followed by explanation."
        comparison_result = llm.invoke(comparison_prompt)
        
        if "MATCH" in comparison_result.content.upper():
            match_result = f"LinkedIn profile verified. Names match: Resume: '{resume_name}' vs LinkedIn: '{linkedin_name}'"
        else:
            match_result = f"LinkedIn profile found but names may not match. Resume: '{resume_name}' vs LinkedIn: '{linkedin_name}'"

    except Exception as ex:
        print("Error scraping LinkedIn: {}".format(ex))
        match_result = "Error accessing LinkedIn profile. This may be due to LinkedIn's anti-scraping measures."

    return {"messages": [match_result]}

# Recruiter Agent: aggregates all info and makes the final fit judgment
def recruiter_agent(state: AgentState):
    try:
        # Collect all relevant info from previous agents
        resume_name = state["messages"][0] if len(state["messages"]) > 0 else "Unknown"
        job_requirements = state["messages"][1] if len(state["messages"]) > 1 else "Unknown"
        linkedin_result = state["messages"][2] if len(state["messages"]) > 2 else "Unknown"
        verification_result = state["messages"][3] if len(state["messages"]) > 3 else "Unknown"

        # Also get the actual resume content for better analysis
        try:
            pdf_file = "Resume.pdf"
            data = PyPDFLoader(pdf_file).load()
            string_text = [data[i].page_content for i in range(len(data))]
            resume_content = " ".join([text for text in string_text])[:3000]  # Limit length
        except:
            resume_content = "Resume content not available"

        # Compose a prompt for the LLM to make a final fit judgment
        prompt = f"""You are an expert recruiter. Analyze the following information and provide a comprehensive evaluation:

CANDIDATE: {resume_name}

JOB REQUIREMENTS:
{job_requirements}

RESUME CONTENT SUMMARY:
{resume_content}

LINKEDIN VERIFICATION:
{verification_result}

Based on this information, provide:
1. A summary of the candidate's key qualifications
2. How well the candidate matches the job requirements (rate 1-10)
3. Strengths and potential concerns
4. Final recommendation (HIRE, CONSIDER, or REJECT)
5. Brief explanation of your decision

Be thorough but concise in your analysis."""

        result = llm.invoke(prompt)
        final_judgment = result.content

    except Exception as ex:
        print("Error in recruiter agent: {}".format(ex))
        final_judgment = "Error making final judgment."

    return {"messages": [final_judgment]}

# Create the LangGraph workflow
def create_workflow():
    """Create and return the LangGraph workflow"""
    workflow = StateGraph(AgentState)

    # Add nodes for each agent
    workflow.add_node("resume_agent", agent)
    workflow.add_node("job_description_agent", job_description_agent)
    workflow.add_node("rag_agent", rag_output)
    workflow.add_node("web_scraping_agent", web_scrap_agent)
    workflow.add_node("recruiter_agent", recruiter_agent)

    # Define the workflow edges
    workflow.add_edge(START, "resume_agent")
    workflow.add_edge("resume_agent", "job_description_agent")
    workflow.add_edge("job_description_agent", "rag_agent")
    workflow.add_edge("rag_agent", "web_scraping_agent")
    workflow.add_edge("web_scraping_agent", "recruiter_agent")
    workflow.add_edge("recruiter_agent", END)

    # Compile the workflow
    return workflow.compile()

# Main function: sets up and runs the Streamlit app
def main():
    # Set the Streamlit app title
    st.title("Resume Matching App with Multi-Agent Workflow")
    
    # Display embedding model info
    st.info(f"Using embedding model: {EMBEDDING_TYPE}")

    # Upload the resume PDF
    resume_file = st.file_uploader("Upload Resume (PDF)", type=["pdf"])
    if resume_file is not None:
        # Save the uploaded PDF locally
        with open("Resume.pdf", "wb") as f:
            f.write(resume_file.read())
        st.success("Resume uploaded successfully.")

    # Upload the job description text file
    jd_file = st.file_uploader("Upload Job Description (TXT)", type=["txt"])
    if jd_file is not None:
        # Save the uploaded JD locally
        with open("JobDescription.txt", "wb") as f:
            f.write(jd_file.read())
        st.success("Job Description uploaded successfully.")

    # Check if both files are uploaded
    import os
    if not (os.path.exists("Resume.pdf") and os.path.exists("JobDescription.txt")):
        st.warning("Please upload both Resume (PDF) and Job Description (TXT) files before proceeding.")
        return

    # Button to trigger the resume matching workflow
    if st.button("Match Resume"):
        st.write("Running Multi-Agent Workflow...")
        
        # Create progress bar
        progress_bar = st.progress(0)
        status_text = st.empty()

        try:
            # Create the LangGraph workflow
            workflow = create_workflow()

            # Initialize the agent state with an empty message list
            initial_state = {
                "messages": [],
                "candidate_name": "",
                "job_requirements": "",
                "linkedin_url": "",
                "linkedin_verification": "",
                "final_judgment": ""
            }

            # Execute the workflow
            final_state = workflow.invoke(initial_state)
            progress_bar.progress(100)
            status_text.text("Workflow completed successfully!")

            # Display results
            st.write("## Workflow Results")
            
            agent_names = [
                "👤 Resume Agent (Name Extraction)", 
                "📋 Job Description Agent (Requirements)", 
                "🔍 RAG Agent (LinkedIn Extraction)", 
                "🌐 Web Scraping Agent (LinkedIn Verification)", 
                "🎯 Recruiter Agent (Final Assessment)"
            ]
            
            for i, message in enumerate(final_state["messages"]):
                if i < len(agent_names):
                    with st.expander(f"{agent_names[i]}", expanded=(i == len(final_state["messages"]) - 1)):
                        st.write(message)

        except Exception as e:
            st.error(f"Error running workflow: {str(e)}")
            progress_bar.progress(0)
            status_text.text("Workflow failed, trying fallback...")

            # Fallback to sequential execution
            st.write("## Fallback Sequential Execution")
            agent_state = {"messages": []}

            # Run each agent in sequence and collect their outputs
            agents = [
                ("👤 Resume Agent", agent),
                ("📋 Job Description Agent", job_description_agent),
                ("🔍 RAG Agent", rag_output),
                ("🌐 Web Scraping Agent", web_scrap_agent),
                ("🎯 Recruiter Agent", recruiter_agent)
            ]

            for i, (agent_name, agent_func) in enumerate(agents):
                try:
                    status_text.text(f"Running {agent_name}...")
                    progress_bar.progress((i + 1) * 20)
                    
                    output = agent_func(agent_state)
                    agent_state["messages"].extend(output["messages"])
                    
                    with st.expander(f"{agent_name} Output", expanded=(i == len(agents) - 1)):
                        st.write(output["messages"][0])
                        
                except Exception as agent_error:
                    st.error(f"Error in {agent_name}: {str(agent_error)}")
                    agent_state["messages"].append(f"Error in {agent_name}")

            status_text.text("Sequential execution completed!")

# Entry point for the script
if __name__ == "__main__":
    main()