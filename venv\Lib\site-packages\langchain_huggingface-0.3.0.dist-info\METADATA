Metadata-Version: 2.1
Name: langchain-huggingface
Version: 0.3.0
Summary: An integration package connecting Hugging Face and LangChain.
License: MIT
Project-URL: Source Code, https://github.com/langchain-ai/langchain/tree/master/libs/partners/huggingface
Project-URL: Release Notes, https://github.com/langchain-ai/langchain/releases?q=tag%3A%22langchain-huggingface%3D%3D0%22&expanded=true
Project-URL: repository, https://github.com/langchain-ai/langchain
Requires-Python: >=3.9
Requires-Dist: langchain-core<1.0.0,>=0.3.65
Requires-Dist: tokenizers>=0.19.1
Requires-Dist: huggingface-hub>=0.30.2
Provides-Extra: full
Requires-Dist: transformers>=4.39.0; extra == "full"
Requires-Dist: sentence-transformers>=2.6.0; extra == "full"
Description-Content-Type: text/markdown

# langchain-huggingface

This package contains the LangChain integrations for huggingface related classes.

## Installation and Setup

- Install the LangChain partner package
```bash
pip install langchain-huggingface
```
