# Resume Matching Multi-Agent AI App

A sophisticated multi-agent AI application that matches resumes with job descriptions using Lang<PERSON>raph, <PERSON><PERSON><PERSON><PERSON>, and Streamlit.

## 🚀 Features

- **Multi-Agent Workflow**: Orchestrated using LangGraph for efficient task distribution
- **Resume Analysis**: Extracts candidate information from PDF resumes
- **Job Description Processing**: Analyzes job requirements from text files
- **LinkedIn Profile Verification**: Web scraping to verify candidate profiles
- **RAG (Retrieval Augmented Generation)**: Semantic search using vector databases
- **AI-Powered Matching**: Uses Llama 3 via Groq API for intelligent analysis
- **Interactive Web Interface**: Built with Streamlit for easy use

## 🛠️ Technology Stack

- **LangGraph**: Multi-agent workflow orchestration
- **LangChain**: LLM framework and document processing
- **Groq API**: Fast inference with Llama 3 model
- **Streamlit**: Web application framework
- **ChromaDB**: Vector database for semantic search
- **HuggingFace**: Embeddings and transformers
- **PyPDF**: PDF document processing
- **BeautifulSoup**: Web scraping capabilities

## 📋 Prerequisites

- Python 3.11+
- Groq API key
- Git

## 🔧 Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/raj3176/Resume-Match-Multi-Agent-AI-App.git
   cd Resume-Match-Multi-Agent-AI-App
   ```

2. **Set up virtual environment**
   ```bash
   # On Windows
   python -m venv venv
   venv\Scripts\activate
   
   # On macOS/Linux
   python -m venv venv
   source venv/bin/activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables**
   Create a `.env` file in the root directory:
   ```env
   GROQ_API_KEY=your_groq_api_key_here
   ```

## 🚀 Usage

1. **Start the Streamlit application**
   ```bash
   cd multi-agent-workflow
   streamlit run app_agent5d.py
   ```

2. **Upload files**
   - Upload a resume (PDF format)
   - Upload a job description (TXT format)

3. **Run the matching process**
   - Click "Match Resume" to start the multi-agent workflow
   - View results from each agent in the workflow

## 🔄 Multi-Agent Workflow

The application uses a sophisticated multi-agent workflow:

1. **Resume Agent**: Extracts candidate name and information
2. **Job Description Agent**: Analyzes job requirements
3. **RAG Agent**: Performs semantic search and retrieval
4. **Web Scraping Agent**: Verifies LinkedIn profiles
5. **Recruiter Agent**: Makes final matching decisions

## 📁 Project Structure

```
Resume-Match-Multi-Agent-AI-App/
├── multi-agent-workflow/
│   ├── app_agent5d.py          # Main working application
│   ├── app.py                  # LangGraph implementation
│   ├── workflow.png            # Workflow diagram
│   └── setup_*.bat            # Setup scripts
├── requirements.txt            # Python dependencies
├── .gitignore                 # Git ignore rules
└── README.md                  # This file
```

## 🔑 Key Features

### LangGraph Integration
- Proper StateGraph implementation
- Sequential agent execution
- State management between agents

### AI-Powered Analysis
- Llama 3 model via Groq API
- Semantic similarity matching
- Intelligent resume parsing

### Vector Database
- ChromaDB for document storage
- HuggingFace embeddings
- Efficient similarity search

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- LangChain team for the excellent framework
- Groq for fast LLM inference
- Streamlit for the web framework
- HuggingFace for embeddings and models

## 📞 Contact

- GitHub: [@raj3176](https://github.com/raj3176)
- Project Link: [https://github.com/raj3176/Resume-Match-Multi-Agent-AI-App](https://github.com/raj3176/Resume-Match-Multi-Agent-AI-App)
